<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 购物商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">电子产品</a></li>
                            <li><a href="products.html?category=clothing">服装</a></li>
                            <li><a href="products.html?category=books">图书</a></li>
                            <li><a href="products.html?category=home">家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">购物车 <span id="cart-count">0</span></a></li>
                    <li><a href="user.html">用户中心</a></li>
                </ul>
            </nav>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索商品...">
                <div id="search-suggestions"></div>
                <button onclick="searchProducts()">搜索</button>
            </div>
        </div>
    </header>

    <main>
        <section class="product-detail-section">
            <div class="container">
                <div class="product-detail">
                    <div class="product-images">
                        <div class="main-image-container">
                            <div class="main-image" id="main-image">
                                <div class="zoom-lens" id="zoom-lens"></div>
                                <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; height: 400px; width: 100%;">智能手机</div>
                            </div>
                            <div class="zoom-result" id="zoom-result"></div>
                        </div>
                        <div class="thumbnail-images">
                            <div class="thumbnail active" onclick="changeMainImage(0)">
                                <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; height: 80px;">图1</div>
                            </div>
                            <div class="thumbnail" onclick="changeMainImage(1)">
                                <div class="product-img" style="background: linear-gradient(45deg, #f093fb, #f5576c); color: white; display: flex; align-items: center; justify-content: center; height: 80px;">图2</div>
                            </div>
                            <div class="thumbnail" onclick="changeMainImage(2)">
                                <div class="product-img" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; display: flex; align-items: center; justify-content: center; height: 80px;">图3</div>
                            </div>
                        </div>
                    </div>

                    <div class="product-info">
                        <h1 id="product-title">智能手机</h1>
                        <p class="product-price" id="product-price">¥2999</p>
                        <div class="product-description">
                            <h3>商品描述</h3>
                            <p id="product-description">这是一款功能强大的智能手机，配备最新的处理器和高清摄像头，为您带来卓越的使用体验。</p>
                        </div>
                        
                        <div class="product-options">
                            <div class="option-group">
                                <label>颜色:</label>
                                <div class="color-options">
                                    <button class="color-option active" data-color="black">黑色</button>
                                    <button class="color-option" data-color="white">白色</button>
                                    <button class="color-option" data-color="blue">蓝色</button>
                                </div>
                            </div>
                            
                            <div class="option-group">
                                <label>存储容量:</label>
                                <div class="storage-options">
                                    <button class="storage-option active" data-storage="128gb">128GB</button>
                                    <button class="storage-option" data-storage="256gb">256GB</button>
                                    <button class="storage-option" data-storage="512gb">512GB</button>
                                </div>
                            </div>
                            
                            <div class="option-group">
                                <label>数量:</label>
                                <div class="quantity-selector">
                                    <button onclick="changeQuantity(-1)">-</button>
                                    <input type="number" id="quantity" value="1" min="1" max="10">
                                    <button onclick="changeQuantity(1)">+</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="product-actions">
                            <button class="btn-primary add-to-cart-detail" onclick="addToCartDetail()">加入购物车</button>
                            <button class="btn-secondary" onclick="buyNow()">立即购买</button>
                        </div>
                    </div>
                </div>

                <div class="product-tabs">
                    <div class="tabs">
                        <button class="tab active" onclick="switchProductTab('details')">详细信息</button>
                        <button class="tab" onclick="switchProductTab('specs')">规格参数</button>
                        <button class="tab" onclick="switchProductTab('reviews')">用户评价</button>
                    </div>
                    
                    <div class="tab-content active" id="details">
                        <h3>详细信息</h3>
                        <p>这款智能手机采用最新的技术，为用户提供出色的性能和体验。</p>
                        <ul>
                            <li>高性能处理器，运行流畅</li>
                            <li>高清摄像头，拍照清晰</li>
                            <li>长续航电池，持久使用</li>
                            <li>精美外观设计，手感舒适</li>
                        </ul>
                    </div>
                    
                    <div class="tab-content" id="specs">
                        <h3>规格参数</h3>
                        <table class="specs-table">
                            <tr><td>屏幕尺寸</td><td>6.1英寸</td></tr>
                            <tr><td>分辨率</td><td>2532 x 1170</td></tr>
                            <tr><td>处理器</td><td>A15仿生芯片</td></tr>
                            <tr><td>内存</td><td>6GB</td></tr>
                            <tr><td>电池容量</td><td>3095mAh</td></tr>
                            <tr><td>操作系统</td><td>iOS 15</td></tr>
                        </table>
                    </div>
                    
                    <div class="tab-content" id="reviews">
                        <h3>用户评价</h3>
                        <div class="review-item">
                            <div class="review-header">
                                <span class="reviewer">用户A</span>
                                <span class="rating">★★★★★</span>
                            </div>
                            <p>非常好用的手机，性能强劲，拍照效果很棒！</p>
                        </div>
                        <div class="review-item">
                            <div class="review-header">
                                <span class="reviewer">用户B</span>
                                <span class="rating">★★★★☆</span>
                            </div>
                            <p>整体不错，就是价格有点贵，但是物有所值。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 购物商城. 保留所有权利.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/product-detail.js"></script>
</body>
</html>
