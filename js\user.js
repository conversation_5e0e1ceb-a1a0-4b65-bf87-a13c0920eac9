document.addEventListener('DOMContentLoaded', function() {
    setupFormValidation();
});

function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('error')) {
                    validateField(this);
                }
            });
        });
    });
}

function validateField(field) {
    const errorElement = field.parentNode.querySelector('.error-message');
    let isValid = true;
    let errorMessage = '';
    
    field.classList.remove('error');
    if (errorElement) {
        errorElement.style.display = 'none';
        errorElement.textContent = '';
    }
    
    if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        errorMessage = '此字段为必填项';
    } else if (field.type === 'email' && field.value && !isValidEmail(field.value)) {
        isValid = false;
        errorMessage = '请输入有效的邮箱地址';
    } else if (field.type === 'password' && field.value && field.value.length < 6) {
        isValid = false;
        errorMessage = '密码至少需要6个字符';
    } else if (field.id === 'confirm-password') {
        const password = document.getElementById('register-password').value;
        if (field.value && field.value !== password) {
            isValid = false;
            errorMessage = '两次输入的密码不一致';
        }
    } else if (field.type === 'tel' && field.value && !isValidPhone(field.value)) {
        isValid = false;
        errorMessage = '请输入有效的手机号码';
    }
    
    if (!isValid) {
        field.classList.add('error');
        if (errorElement) {
            errorElement.textContent = errorMessage;
            errorElement.style.display = 'block';
        }
    }
    
    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

function handleLogin(event) {
    event.preventDefault();
    
    if (!validateForm('login-form')) {
        return false;
    }
    
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;
    const rememberMe = document.getElementById('remember-me').checked;
    
    setTimeout(() => {
        alert('登录成功！');
        switchTab('profile');
    }, 500);
    
    return false;
}

function handleRegister(event) {
    event.preventDefault();
    
    if (!validateForm('register-form')) {
        return false;
    }
    
    const formData = {
        username: document.getElementById('register-username').value,
        email: document.getElementById('register-email').value,
        password: document.getElementById('register-password').value,
        phone: document.getElementById('register-phone').value
    };
    
    setTimeout(() => {
        alert('注册成功！请登录您的账户。');
        switchTab('login');
        document.getElementById('register-form').reset();
    }, 500);
    
    return false;
}

function handleProfileUpdate(event) {
    event.preventDefault();
    
    if (!validateForm('profile-form')) {
        return false;
    }
    
    const formData = {
        username: document.getElementById('profile-username').value,
        email: document.getElementById('profile-email').value,
        phone: document.getElementById('profile-phone').value,
        address: document.getElementById('profile-address').value
    };
    
    setTimeout(() => {
        alert('个人信息更新成功！');
    }, 500);
    
    return false;
}

function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], input[type="email"], input[type="password"], input[type="tel"]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function filterOrders(status) {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const orderItems = document.querySelectorAll('.order-item');
    
    filterBtns.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    orderItems.forEach(item => {
        if (status === 'all' || item.getAttribute('data-status') === status) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}
