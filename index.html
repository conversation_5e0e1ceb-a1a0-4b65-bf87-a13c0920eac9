<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线购物商城 - 首页</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">电子产品</a></li>
                            <li><a href="products.html?category=clothing">服装</a></li>
                            <li><a href="products.html?category=books">图书</a></li>
                            <li><a href="products.html?category=home">家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">购物车 <span id="cart-count">0</span></a></li>
                    <li><a href="user.html">用户中心</a></li>
                </ul>
            </nav>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索商品...">
                <div id="search-suggestions"></div>
                <button onclick="searchProducts()">搜索</button>
            </div>
        </div>
    </header>

    <main>
        <section class="hero-slider">
            <div class="slider-container">
                <div class="slide active">
                    <div class="slide-bg" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);"></div>
                    <div class="slide-content">
                        <h2>春季大促销</h2>
                        <p>全场商品8折起</p>
                        <button class="btn-primary">立即购买</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-bg" style="background: linear-gradient(45deg, #667eea, #764ba2);"></div>
                    <div class="slide-content">
                        <h2>新品上市</h2>
                        <p>最新款式等你来</p>
                        <button class="btn-primary">查看详情</button>
                    </div>
                </div>
                <div class="slide">
                    <div class="slide-bg" style="background: linear-gradient(45deg, #f093fb, #f5576c);"></div>
                    <div class="slide-content">
                        <h2>限时秒杀</h2>
                        <p>每日10点准时开抢</p>
                        <button class="btn-primary">参与秒杀</button>
                    </div>
                </div>
            </div>
            <div class="slider-nav">
                <button class="prev" onclick="changeSlide(-1)">‹</button>
                <button class="next" onclick="changeSlide(1)">›</button>
            </div>
            <div class="slider-dots">
                <span class="dot active" onclick="currentSlide(1)"></span>
                <span class="dot" onclick="currentSlide(2)"></span>
                <span class="dot" onclick="currentSlide(3)"></span>
            </div>
        </section>

        <section class="featured-products">
            <div class="container">
                <h2>热门商品</h2>
                <div class="product-grid">
                    <div class="product-card">
                        <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">智能手机</div>
                        <h3>智能手机</h3>
                        <p class="price">¥2999</p>
                        <button class="add-to-cart" data-id="1" data-name="智能手机" data-price="2999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-img" style="background: linear-gradient(45deg, #f093fb, #f5576c); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">笔记本电脑</div>
                        <h3>笔记本电脑</h3>
                        <p class="price">¥5999</p>
                        <button class="add-to-cart" data-id="2" data-name="笔记本电脑" data-price="5999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-img" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">无线耳机</div>
                        <h3>无线耳机</h3>
                        <p class="price">¥299</p>
                        <button class="add-to-cart" data-id="3" data-name="无线耳机" data-price="299">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-img" style="background: linear-gradient(45deg, #a8edea, #fed6e3); color: #333; display: flex; align-items: center; justify-content: center; height: 200px;">智能手表</div>
                        <h3>智能手表</h3>
                        <p class="price">¥1299</p>
                        <button class="add-to-cart" data-id="4" data-name="智能手表" data-price="1299">加入购物车</button>
                    </div>
                </div>
            </div>
        </section>

        <section class="countdown-section">
            <div class="container">
                <h2>限时抢购</h2>
                <div class="countdown-timer">
                    <div class="time-unit">
                        <span id="hours">00</span>
                        <label>时</label>
                    </div>
                    <div class="time-unit">
                        <span id="minutes">00</span>
                        <label>分</label>
                    </div>
                    <div class="time-unit">
                        <span id="seconds">00</span>
                        <label>秒</label>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 购物商城. 保留所有权利.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
