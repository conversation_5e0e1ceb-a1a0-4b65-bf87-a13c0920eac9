<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线购物商城 - 首页</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>🛒 购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html" class="active">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类 ▼</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">📱 电子产品</a></li>
                            <li><a href="products.html?category=clothing">👕 服装</a></li>
                            <li><a href="products.html?category=books">📚 图书</a></li>
                            <li><a href="products.html?category=home">🏠 家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">🛒 购物车 <span class="cart-badge">3</span></a></li>
                    <li><a href="user.html">👤 用户中心</a></li>
                </ul>
            </nav>
            <div class="search-container">
                <input type="text" placeholder="搜索您想要的商品..." class="search-input">
                <button class="search-btn">🔍</button>
                <div class="search-suggestions">
                    <div class="suggestion-item">智能手机</div>
                    <div class="suggestion-item">笔记本电脑</div>
                    <div class="suggestion-item">无线耳机</div>
                </div>
            </div>
        </div>
    </header>

    <main>
        <!-- 纯CSS轮播图 -->
        <section class="hero-section">
            <div class="slider">
                <input type="radio" name="slide" id="slide1" checked>
                <input type="radio" name="slide" id="slide2">
                <input type="radio" name="slide" id="slide3">

                <div class="slides">
                    <div class="slide slide1">
                        <div class="slide-content">
                            <h2>🎉 春季大促销</h2>
                            <p>全场商品8折起，限时优惠不容错过</p>
                            <a href="products.html" class="cta-btn">立即购买</a>
                        </div>
                    </div>
                    <div class="slide slide2">
                        <div class="slide-content">
                            <h2>✨ 新品上市</h2>
                            <p>最新款式等你来，品质保证</p>
                            <a href="products.html" class="cta-btn">查看详情</a>
                        </div>
                    </div>
                    <div class="slide slide3">
                        <div class="slide-content">
                            <h2>⚡ 限时秒杀</h2>
                            <p>每日10点准时开抢，手慢无</p>
                            <a href="products.html" class="cta-btn">参与秒杀</a>
                        </div>
                    </div>
                </div>

                <div class="navigation">
                    <label for="slide1" class="nav-btn"></label>
                    <label for="slide2" class="nav-btn"></label>
                    <label for="slide3" class="nav-btn"></label>
                </div>
            </div>
        </section>

        <!-- 热门商品区域 -->
        <section class="featured-products">
            <div class="container">
                <h2>🔥 热门商品</h2>
                <div class="product-grid">
                    <div class="product-card">
                        <div class="product-badge">热销</div>
                        <div class="product-img phone">
                            <span class="product-icon">📱</span>
                        </div>
                        <div class="product-info">
                            <h3>智能手机</h3>
                            <p class="product-desc">高性能处理器，超清摄像</p>
                            <div class="price-section">
                                <span class="current-price">¥2999</span>
                                <span class="original-price">¥3999</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(128评价)</span>
                            </div>
                            <a href="product-detail.html?id=1" class="add-to-cart">立即购买</a>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-badge new">新品</div>
                        <div class="product-img laptop">
                            <span class="product-icon">💻</span>
                        </div>
                        <div class="product-info">
                            <h3>笔记本电脑</h3>
                            <p class="product-desc">轻薄便携，办公娱乐</p>
                            <div class="price-section">
                                <span class="current-price">¥5999</span>
                                <span class="original-price">¥7999</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(89评价)</span>
                            </div>
                            <a href="product-detail.html?id=2" class="add-to-cart">立即购买</a>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-badge discount">特价</div>
                        <div class="product-img earphone">
                            <span class="product-icon">🎧</span>
                        </div>
                        <div class="product-info">
                            <h3>无线耳机</h3>
                            <p class="product-desc">降噪技术，音质清晰</p>
                            <div class="price-section">
                                <span class="current-price">¥299</span>
                                <span class="original-price">¥599</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(256评价)</span>
                            </div>
                            <a href="product-detail.html?id=3" class="add-to-cart">立即购买</a>
                        </div>
                    </div>

                    <div class="product-card">
                        <div class="product-badge">推荐</div>
                        <div class="product-img watch">
                            <span class="product-icon">⌚</span>
                        </div>
                        <div class="product-info">
                            <h3>智能手表</h3>
                            <p class="product-desc">健康监测，智能提醒</p>
                            <div class="price-section">
                                <span class="current-price">¥1299</span>
                                <span class="original-price">¥1899</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(167评价)</span>
                            </div>
                            <a href="product-detail.html?id=4" class="add-to-cart">立即购买</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 纯CSS倒计时效果 -->
        <section class="countdown-section">
            <div class="container">
                <h2>⏰ 限时抢购</h2>
                <p class="countdown-desc">距离活动结束还有</p>
                <div class="countdown-display">
                    <div class="time-unit">
                        <div class="time-number">02</div>
                        <div class="time-label">时</div>
                    </div>
                    <div class="time-separator">:</div>
                    <div class="time-unit">
                        <div class="time-number">30</div>
                        <div class="time-label">分</div>
                    </div>
                    <div class="time-separator">:</div>
                    <div class="time-unit">
                        <div class="time-number">45</div>
                        <div class="time-label">秒</div>
                    </div>
                </div>
                <div class="flash-sale-products">
                    <div class="flash-item">
                        <div class="flash-img">🎮</div>
                        <div class="flash-info">
                            <h4>游戏手柄</h4>
                            <span class="flash-price">¥99</span>
                        </div>
                    </div>
                    <div class="flash-item">
                        <div class="flash-img">📷</div>
                        <div class="flash-info">
                            <h4>数码相机</h4>
                            <span class="flash-price">¥1999</span>
                        </div>
                    </div>
                    <div class="flash-item">
                        <div class="flash-img">🖱️</div>
                        <div class="flash-info">
                            <h4>无线鼠标</h4>
                            <span class="flash-price">¥59</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>🛒 购物商城</h3>
                    <p>您身边的购物专家</p>
                    <div class="social-links">
                        <span>📱</span>
                        <span>📧</span>
                        <span>🌐</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>商品分类</h4>
                    <ul>
                        <li><a href="products.html?category=electronics">电子产品</a></li>
                        <li><a href="products.html?category=clothing">服装配饰</a></li>
                        <li><a href="products.html?category=books">图书文具</a></li>
                        <li><a href="products.html?category=home">家居用品</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>客户服务</h4>
                    <ul>
                        <li><a href="#">帮助中心</a></li>
                        <li><a href="#">退换货政策</a></li>
                        <li><a href="#">配送说明</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <ul>
                        <li><a href="#">公司介绍</a></li>
                        <li><a href="#">招聘信息</a></li>
                        <li><a href="#">合作伙伴</a></li>
                        <li><a href="#">隐私政策</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 购物商城. 保留所有权利. | 京ICP备12345678号</p>
            </div>
        </div>
    </footer>

    <!-- 最小化JavaScript，仅用于必要功能 -->
    <script>
        // 搜索建议显示/隐藏
        document.querySelector('.search-input').addEventListener('focus', function() {
            document.querySelector('.search-suggestions').style.display = 'block';
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-container')) {
                document.querySelector('.search-suggestions').style.display = 'none';
            }
        });

        // 购物车数量更新（模拟）
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const badge = document.querySelector('.cart-badge');
                let count = parseInt(badge.textContent) || 0;
                badge.textContent = count + 1;

                // 简单的成功提示
                const originalText = this.textContent;
                this.textContent = '已添加 ✓';
                this.style.background = '#28a745';

                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.background = '';
                }, 1000);
            });
        });
    </script>
</body>
</html>
