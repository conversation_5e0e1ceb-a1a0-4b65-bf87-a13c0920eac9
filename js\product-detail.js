const productData = {
    '1': {
        title: '智能手机',
        price: 2999,
        description: '这是一款功能强大的智能手机，配备最新的处理器和高清摄像头，为您带来卓越的使用体验。',
        images: [
            { bg: 'linear-gradient(45deg, #667eea, #764ba2)', text: '智能手机' },
            { bg: 'linear-gradient(45deg, #f093fb, #f5576c)', text: '智能手机' },
            { bg: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)', text: '智能手机' }
        ]
    },
    '2': {
        title: '笔记本电脑',
        price: 5999,
        description: '轻薄便携的笔记本电脑，适合办公和娱乐使用，性能卓越。',
        images: [
            { bg: 'linear-gradient(45deg, #f093fb, #f5576c)', text: '笔记本电脑' },
            { bg: 'linear-gradient(45deg, #667eea, #764ba2)', text: '笔记本电脑' },
            { bg: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)', text: '笔记本电脑' }
        ]
    }
};

let currentImageIndex = 0;
let selectedColor = 'black';
let selectedStorage = '128gb';

document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id') || '1';
    
    loadProductData(productId);
    initializeImageZoom();
    initializeOptions();
});

function loadProductData(productId) {
    const product = productData[productId];
    if (!product) return;
    
    document.getElementById('product-title').textContent = product.title;
    document.getElementById('product-price').textContent = `¥${product.price}`;
    document.getElementById('product-description').textContent = product.description;
    
    updateMainImage(0, product.images);
}

function changeMainImage(index) {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id') || '1';
    const product = productData[productId];
    
    if (!product) return;
    
    currentImageIndex = index;
    updateMainImage(index, product.images);
    
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumb => thumb.classList.remove('active'));
    thumbnails[index].classList.add('active');
}

function updateMainImage(index, images) {
    const mainImage = document.querySelector('#main-image .product-img');
    const image = images[index];
    
    mainImage.style.background = image.bg;
    mainImage.textContent = image.text;
}

function initializeImageZoom() {
    const mainImage = document.getElementById('main-image');
    const lens = document.getElementById('zoom-lens');
    const result = document.getElementById('zoom-result');
    
    mainImage.addEventListener('mousemove', function(e) {
        const rect = mainImage.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const lensWidth = 100;
        const lensHeight = 100;
        
        let lensX = x - lensWidth / 2;
        let lensY = y - lensHeight / 2;
        
        if (lensX < 0) lensX = 0;
        if (lensY < 0) lensY = 0;
        if (lensX > rect.width - lensWidth) lensX = rect.width - lensWidth;
        if (lensY > rect.height - lensHeight) lensY = rect.height - lensHeight;
        
        lens.style.left = lensX + 'px';
        lens.style.top = lensY + 'px';
        lens.style.display = 'block';
        
        const zoomX = (lensX / rect.width) * 100;
        const zoomY = (lensY / rect.height) * 100;
        
        const productImg = mainImage.querySelector('.product-img');
        result.style.background = productImg.style.background;
        result.style.backgroundSize = '400% 400%';
        result.style.backgroundPosition = `${zoomX}% ${zoomY}%`;
        result.style.display = 'block';
        result.innerHTML = `<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 2rem;">${productImg.textContent}</div>`;
    });
    
    mainImage.addEventListener('mouseleave', function() {
        lens.style.display = 'none';
        result.style.display = 'none';
    });
}

function initializeOptions() {
    const colorOptions = document.querySelectorAll('.color-option');
    const storageOptions = document.querySelectorAll('.storage-option');
    
    colorOptions.forEach(option => {
        option.addEventListener('click', function() {
            colorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            selectedColor = this.getAttribute('data-color');
        });
    });
    
    storageOptions.forEach(option => {
        option.addEventListener('click', function() {
            storageOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            selectedStorage = this.getAttribute('data-storage');
            updatePrice();
        });
    });
}

function updatePrice() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id') || '1';
    const product = productData[productId];
    
    if (!product) return;
    
    let basePrice = product.price;
    let additionalPrice = 0;
    
    if (selectedStorage === '256gb') {
        additionalPrice = 500;
    } else if (selectedStorage === '512gb') {
        additionalPrice = 1000;
    }
    
    const totalPrice = basePrice + additionalPrice;
    document.getElementById('product-price').textContent = `¥${totalPrice}`;
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('quantity');
    let quantity = parseInt(quantityInput.value) + change;
    
    if (quantity < 1) quantity = 1;
    if (quantity > 10) quantity = 10;
    
    quantityInput.value = quantity;
}

function switchProductTab(tabName) {
    const tabs = document.querySelectorAll('.product-tabs .tab');
    const tabContents = document.querySelectorAll('.product-tabs .tab-content');
    
    tabs.forEach(tab => tab.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));
    
    document.querySelector(`[onclick="switchProductTab('${tabName}')"]`).classList.add('active');
    document.getElementById(tabName).classList.add('active');
}

function addToCartDetail() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id') || '1';
    const product = productData[productId];
    
    if (!product) return;
    
    const quantity = parseInt(document.getElementById('quantity').value);
    const price = parseFloat(document.getElementById('product-price').textContent.replace('¥', ''));
    
    for (let i = 0; i < quantity; i++) {
        addToCart(productId, product.title, price);
    }
}

function buyNow() {
    addToCartDetail();
    window.location.href = 'cart.html';
}
