<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表 - 购物商城</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>🛒 购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html" class="active">商品分类 ▼</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">📱 电子产品</a></li>
                            <li><a href="products.html?category=clothing">👕 服装</a></li>
                            <li><a href="products.html?category=books">📚 图书</a></li>
                            <li><a href="products.html?category=home">🏠 家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">🛒 购物车 <span class="cart-badge">5</span></a></li>
                    <li><a href="user.html">👤 用户中心</a></li>
                </ul>
            </nav>
            <div class="search-container">
                <input type="text" placeholder="搜索您想要的商品..." class="search-input">
                <button class="search-btn">🔍</button>
            </div>
        </div>
    </header>

    <main>
        <section class="products-section">
            <div class="container">
                <div class="products-header">
                    <h1>🛍️ 商品列表</h1>
                    <!-- 纯CSS筛选器 -->
                    <div class="filter-tabs">
                        <input type="radio" name="category" id="all" checked>
                        <input type="radio" name="category" id="electronics">
                        <input type="radio" name="category" id="clothing">
                        <input type="radio" name="category" id="books">
                        <input type="radio" name="category" id="home">

                        <div class="tab-labels">
                            <label for="all" class="filter-tab">全部</label>
                            <label for="electronics" class="filter-tab">📱 电子产品</label>
                            <label for="clothing" class="filter-tab">👕 服装</label>
                            <label for="books" class="filter-tab">📚 图书</label>
                            <label for="home" class="filter-tab">🏠 家居</label>
                        </div>
                    </div>
                </div>

                <div class="products-grid">
                    <!-- 电子产品 -->
                    <div class="product-card electronics">
                        <div class="product-badge">热销</div>
                        <div class="product-img phone">
                            <span class="product-icon">📱</span>
                        </div>
                        <div class="product-info">
                            <h3>智能手机</h3>
                            <p class="product-desc">高性能处理器，超清摄像</p>
                            <div class="price-section">
                                <span class="current-price">¥2999</span>
                                <span class="original-price">¥3999</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(128评价)</span>
                            </div>
                            <a href="product-detail.html?id=1" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <div class="product-card electronics">
                        <div class="product-badge new">新品</div>
                        <div class="product-img laptop">
                            <span class="product-icon">💻</span>
                        </div>
                        <div class="product-info">
                            <h3>笔记本电脑</h3>
                            <p class="product-desc">轻薄便携，办公娱乐</p>
                            <div class="price-section">
                                <span class="current-price">¥5999</span>
                                <span class="original-price">¥7999</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(89评价)</span>
                            </div>
                            <a href="product-detail.html?id=2" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <div class="product-card electronics">
                        <div class="product-badge discount">特价</div>
                        <div class="product-img earphone">
                            <span class="product-icon">🎧</span>
                        </div>
                        <div class="product-info">
                            <h3>无线耳机</h3>
                            <p class="product-desc">降噪技术，音质清晰</p>
                            <div class="price-section">
                                <span class="current-price">¥299</span>
                                <span class="original-price">¥599</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(256评价)</span>
                            </div>
                            <a href="product-detail.html?id=3" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <div class="product-card electronics">
                        <div class="product-badge">推荐</div>
                        <div class="product-img watch">
                            <span class="product-icon">⌚</span>
                        </div>
                        <div class="product-info">
                            <h3>智能手表</h3>
                            <p class="product-desc">健康监测，智能提醒</p>
                            <div class="price-section">
                                <span class="current-price">¥1299</span>
                                <span class="original-price">¥1899</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(167评价)</span>
                            </div>
                            <a href="product-detail.html?id=4" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <!-- 服装类 -->
                    <div class="product-card clothing">
                        <div class="product-badge new">新品</div>
                        <div class="product-img" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);">
                            <span class="product-icon">👕</span>
                        </div>
                        <div class="product-info">
                            <h3>时尚T恤</h3>
                            <p class="product-desc">纯棉材质，舒适透气</p>
                            <div class="price-section">
                                <span class="current-price">¥199</span>
                                <span class="original-price">¥299</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(89评价)</span>
                            </div>
                            <a href="product-detail.html?id=5" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <div class="product-card clothing">
                        <div class="product-badge">热销</div>
                        <div class="product-img" style="background: linear-gradient(135deg, #a18cd1, #fbc2eb);">
                            <span class="product-icon">👖</span>
                        </div>
                        <div class="product-info">
                            <h3>休闲裤</h3>
                            <p class="product-desc">修身版型，百搭款式</p>
                            <div class="price-section">
                                <span class="current-price">¥399</span>
                                <span class="original-price">¥599</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(156评价)</span>
                            </div>
                            <a href="product-detail.html?id=6" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <!-- 图书类 -->
                    <div class="product-card books">
                        <div class="product-badge">推荐</div>
                        <div class="product-img" style="background: linear-gradient(135deg, #fad0c4, #ffd1ff);">
                            <span class="product-icon">📚</span>
                        </div>
                        <div class="product-info">
                            <h3>JavaScript编程指南</h3>
                            <p class="product-desc">从入门到精通，实战案例丰富</p>
                            <div class="price-section">
                                <span class="current-price">¥59</span>
                                <span class="original-price">¥89</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(234评价)</span>
                            </div>
                            <a href="product-detail.html?id=7" class="add-to-cart">查看详情</a>
                        </div>
                    </div>

                    <!-- 家居类 -->
                    <div class="product-card home">
                        <div class="product-badge discount">特价</div>
                        <div class="product-img" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">
                            <span class="product-icon">💡</span>
                        </div>
                        <div class="product-info">
                            <h3>LED护眼台灯</h3>
                            <p class="product-desc">护眼光源，多档调节</p>
                            <div class="price-section">
                                <span class="current-price">¥899</span>
                                <span class="original-price">¥1299</span>
                            </div>
                            <div class="product-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span class="rating-text">(78评价)</span>
                            </div>
                            <a href="product-detail.html?id=8" class="add-to-cart">查看详情</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>🛒 购物商城</h3>
                    <p>您身边的购物专家</p>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="products.html">商品列表</a></li>
                        <li><a href="cart.html">购物车</a></li>
                        <li><a href="user.html">用户中心</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 购物商城. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        const searchBtn = document.querySelector('.search-btn');

        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const searchQuery = urlParams.get('search');

        // 如果有搜索参数，显示在搜索框中并执行搜索
        if (searchQuery) {
            searchInput.value = searchQuery;
            performSearch(searchQuery);
        }

        // 搜索按钮功能
        searchBtn.addEventListener('click', function() {
            const query = searchInput.value.trim();
            if (query) {
                performSearch(query);
            }
        });

        // 回车搜索
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    performSearch(query);
                }
            }
        });

        // 执行搜索
        function performSearch(query) {
            const productCards = document.querySelectorAll('.product-card');
            const lowerQuery = query.toLowerCase();

            productCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const desc = card.querySelector('.product-desc').textContent.toLowerCase();

                if (title.includes(lowerQuery) || desc.includes(lowerQuery)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });

            // 取消所有筛选器选中状态
            document.querySelectorAll('input[name="category"]').forEach(input => {
                input.checked = false;
            });
        }

        // 筛选功能增强
        document.querySelectorAll('input[name="category"]').forEach(input => {
            input.addEventListener('change', function() {
                // 清空搜索框
                searchInput.value = '';

                // 更新URL
                if (this.id !== 'all') {
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.set('category', this.id);
                    newUrl.searchParams.delete('search');
                    window.history.pushState({}, '', newUrl);
                } else {
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.delete('category');
                    newUrl.searchParams.delete('search');
                    window.history.pushState({}, '', newUrl);
                }
            });
        });

        // 根据URL参数设置初始筛选状态
        const categoryParam = urlParams.get('category');
        if (categoryParam) {
            const categoryInput = document.getElementById(categoryParam);
            if (categoryInput) {
                categoryInput.checked = true;
            }
        }

        // 加入购物车功能
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                // 更新购物车数量
                const badge = document.querySelector('.cart-badge');
                let count = parseInt(badge.textContent) || 0;
                badge.textContent = count + 1;

                // 简单的成功提示
                const originalText = this.textContent;
                this.textContent = '已添加 ✓';
                this.style.background = '#28a745';

                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.background = '';
                }, 1000);
            });
        });
    </script>
</body>
</html>
