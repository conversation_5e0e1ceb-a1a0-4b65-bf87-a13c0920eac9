<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表 - 购物商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">电子产品</a></li>
                            <li><a href="products.html?category=clothing">服装</a></li>
                            <li><a href="products.html?category=books">图书</a></li>
                            <li><a href="products.html?category=home">家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">购物车 <span id="cart-count">0</span></a></li>
                    <li><a href="user.html">用户中心</a></li>
                </ul>
            </nav>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索商品...">
                <div id="search-suggestions"></div>
                <button onclick="searchProducts()">搜索</button>
            </div>
        </div>
    </header>

    <main>
        <section class="products-section">
            <div class="container">
                <div class="products-header">
                    <h1>商品列表</h1>
                    <div class="filter-controls">
                        <select id="category-filter" onchange="filterProducts()">
                            <option value="">所有分类</option>
                            <option value="electronics">电子产品</option>
                            <option value="clothing">服装</option>
                            <option value="books">图书</option>
                            <option value="home">家居</option>
                        </select>
                        <select id="price-filter" onchange="filterProducts()">
                            <option value="">价格排序</option>
                            <option value="low-high">价格从低到高</option>
                            <option value="high-low">价格从高到低</option>
                        </select>
                    </div>
                </div>

                <div class="products-grid" id="products-grid">
                    <div class="product-card" data-category="electronics" data-price="2999">
                        <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">智能手机</div>
                        <h3>智能手机</h3>
                        <p class="price">¥2999</p>
                        <p class="description">最新款智能手机，性能强劲</p>
                        <button class="add-to-cart" data-id="1" data-name="智能手机" data-price="2999">加入购物车</button>
                        <a href="product-detail.html?id=1" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="electronics" data-price="5999">
                        <div class="product-img" style="background: linear-gradient(45deg, #f093fb, #f5576c); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">笔记本电脑</div>
                        <h3>笔记本电脑</h3>
                        <p class="price">¥5999</p>
                        <p class="description">轻薄便携，办公娱乐两不误</p>
                        <button class="add-to-cart" data-id="2" data-name="笔记本电脑" data-price="5999">加入购物车</button>
                        <a href="product-detail.html?id=2" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="electronics" data-price="299">
                        <div class="product-img" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">无线耳机</div>
                        <h3>无线耳机</h3>
                        <p class="price">¥299</p>
                        <p class="description">高品质音效，舒适佩戴</p>
                        <button class="add-to-cart" data-id="3" data-name="无线耳机" data-price="299">加入购物车</button>
                        <a href="product-detail.html?id=3" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="electronics" data-price="1299">
                        <div class="product-img" style="background: linear-gradient(45deg, #a8edea, #fed6e3); color: #333; display: flex; align-items: center; justify-content: center; height: 200px;">智能手表</div>
                        <h3>智能手表</h3>
                        <p class="price">¥1299</p>
                        <p class="description">健康监测，智能提醒</p>
                        <button class="add-to-cart" data-id="4" data-name="智能手表" data-price="1299">加入购物车</button>
                        <a href="product-detail.html?id=4" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="clothing" data-price="199">
                        <div class="product-img" style="background: linear-gradient(45deg, #ffecd2, #fcb69f); color: #333; display: flex; align-items: center; justify-content: center; height: 200px;">时尚T恤</div>
                        <h3>时尚T恤</h3>
                        <p class="price">¥199</p>
                        <p class="description">纯棉材质，舒适透气</p>
                        <button class="add-to-cart" data-id="5" data-name="时尚T恤" data-price="199">加入购物车</button>
                        <a href="product-detail.html?id=5" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="clothing" data-price="399">
                        <div class="product-img" style="background: linear-gradient(45deg, #a18cd1, #fbc2eb); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">休闲裤</div>
                        <h3>休闲裤</h3>
                        <p class="price">¥399</p>
                        <p class="description">修身版型，百搭款式</p>
                        <button class="add-to-cart" data-id="6" data-name="休闲裤" data-price="399">加入购物车</button>
                        <a href="product-detail.html?id=6" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="books" data-price="59">
                        <div class="product-img" style="background: linear-gradient(45deg, #fad0c4, #ffd1ff); color: #333; display: flex; align-items: center; justify-content: center; height: 200px;">编程书籍</div>
                        <h3>JavaScript编程指南</h3>
                        <p class="price">¥59</p>
                        <p class="description">从入门到精通，实战案例丰富</p>
                        <button class="add-to-cart" data-id="7" data-name="JavaScript编程指南" data-price="59">加入购物车</button>
                        <a href="product-detail.html?id=7" class="view-details">查看详情</a>
                    </div>

                    <div class="product-card" data-category="home" data-price="899">
                        <div class="product-img" style="background: linear-gradient(45deg, #ff9a9e, #fecfef); color: white; display: flex; align-items: center; justify-content: center; height: 200px;">台灯</div>
                        <h3>LED护眼台灯</h3>
                        <p class="price">¥899</p>
                        <p class="description">护眼光源，多档调节</p>
                        <button class="add-to-cart" data-id="8" data-name="LED护眼台灯" data-price="899">加入购物车</button>
                        <a href="product-detail.html?id=8" class="view-details">查看详情</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 购物商城. 保留所有权利.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/products.js"></script>
</body>
</html>
