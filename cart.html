<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车 - 购物商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">电子产品</a></li>
                            <li><a href="products.html?category=clothing">服装</a></li>
                            <li><a href="products.html?category=books">图书</a></li>
                            <li><a href="products.html?category=home">家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">购物车 <span id="cart-count">0</span></a></li>
                    <li><a href="user.html">用户中心</a></li>
                </ul>
            </nav>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索商品...">
                <div id="search-suggestions"></div>
                <button onclick="searchProducts()">搜索</button>
            </div>
        </div>
    </header>

    <main>
        <section class="cart-section">
            <div class="container">
                <h1>购物车</h1>
                
                <div class="cart-container">
                    <div class="cart-header">
                        <div class="select-all">
                            <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            <label for="select-all">全选</label>
                        </div>
                        <div class="cart-actions">
                            <button onclick="deleteSelected()" class="btn-danger">删除选中</button>
                            <button onclick="clearCart()" class="btn-danger">清空购物车</button>
                        </div>
                    </div>
                    
                    <div class="cart-items" id="cart-items">
                        <!-- 购物车商品将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="cart-empty" id="cart-empty" style="display: none;">
                        <div class="empty-message">
                            <h3>购物车是空的</h3>
                            <p>快去挑选您喜欢的商品吧！</p>
                            <a href="products.html" class="btn-primary">去购物</a>
                        </div>
                    </div>
                    
                    <div class="cart-summary">
                        <div class="summary-row">
                            <span>商品总数：</span>
                            <span id="total-items">0</span>
                        </div>
                        <div class="summary-row">
                            <span>商品总价：</span>
                            <span id="subtotal">¥0.00</span>
                        </div>
                        <div class="summary-row">
                            <span>运费：</span>
                            <span id="shipping">¥0.00</span>
                        </div>
                        <div class="summary-row total">
                            <span>总计：</span>
                            <span id="total-price">¥0.00</span>
                        </div>
                        <button class="checkout-btn" onclick="checkout()">结算</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 购物商城. 保留所有权利.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
</body>
</html>
