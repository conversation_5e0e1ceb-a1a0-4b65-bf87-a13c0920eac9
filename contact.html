<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们</title>
    <style>
        body { font-family: Arial; margin: 0; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        header { background: #f8f9fa; padding: 10px 0; }
        nav ul { list-style: none; display: flex; margin: 0; padding: 0; }
        nav li { margin-right: 20px; }
        nav a { text-decoration: none; color: #333; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .form-group input.error, .form-group textarea.error { border-color: red; }
        .error-msg { color: red; font-size: 14px; margin-top: 5px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .faq { margin-top: 40px; }
        .faq-item { border: 1px solid #ddd; margin: 10px 0; }
        .faq-question { padding: 15px; background: #f8f9fa; cursor: pointer; }
        .faq-answer { padding: 15px; display: none; }
        .checkbox-group { margin: 20px 0; }
        .checkbox-group input { margin-right: 10px; }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1 style="display: inline;">📚 学习平台</h1>
            <nav style="float: right;">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="courses.html">课程</a></li>
                    <li><a href="about.html">关于我们</a></li>
                    <li><a href="contact.html">联系我们</a></li>
                    <li><a href="profile.html">个人中心</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <h1>联系我们</h1>
        
        <form id="contact-form" onsubmit="submitForm(event)">
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" required>
                <div class="error-msg" id="name-error"></div>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱 *</label>
                <input type="email" id="email" name="email" required>
                <div class="error-msg" id="email-error"></div>
            </div>
            
            <div class="form-group">
                <label for="phone">电话</label>
                <input type="tel" id="phone" name="phone">
                <div class="error-msg" id="phone-error"></div>
            </div>
            
            <div class="form-group">
                <label for="subject">主题 *</label>
                <input type="text" id="subject" name="subject" required>
                <div class="error-msg" id="subject-error"></div>
            </div>
            
            <div class="form-group">
                <label for="message">留言内容 *</label>
                <textarea id="message" name="message" rows="5" required></textarea>
                <div class="error-msg" id="message-error"></div>
            </div>
            
            <div class="checkbox-group">
                <label>
                    <input type="checkbox" id="newsletter" onchange="toggleOption(this)">
                    订阅我们的newsletter
                </label>
                <label>
                    <input type="checkbox" id="updates" onchange="toggleOption(this)">
                    接收课程更新通知
                </label>
                <label>
                    <input type="checkbox" id="all-options" onchange="selectAll(this)">
                    全选
                </label>
            </div>
            
            <button type="submit" class="btn">发送留言</button>
        </form>

        <div class="faq">
            <h2>常见问题</h2>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    如何报名课程？ ▼
                </div>
                <div class="faq-answer">
                    您可以在课程页面点击"立即报名"按钮，然后按照指引完成报名流程。
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    课程费用如何计算？ ▼
                </div>
                <div class="faq-answer">
                    课程费用根据课程内容和时长定价，我们经常推出优惠活动，请关注首页公告。
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFAQ(this)">
                    是否提供证书？ ▼
                </div>
                <div class="faq-answer">
                    完成课程学习并通过考核后，我们会颁发结业证书。
                </div>
            </div>
        </div>
    </div>

    <script>
        function submitForm(event) {
            event.preventDefault();
            
            clearErrors();
            
            let isValid = true;
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();
            
            if (name.length < 2) {
                showError('name', '姓名至少需要2个字符');
                isValid = false;
            }
            
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(email)) {
                showError('email', '请输入有效的邮箱地址');
                isValid = false;
            }
            
            if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
                showError('phone', '请输入有效的手机号码');
                isValid = false;
            }
            
            if (subject.length < 3) {
                showError('subject', '主题至少需要3个字符');
                isValid = false;
            }
            
            if (message.length < 10) {
                showError('message', '留言内容至少需要10个字符');
                isValid = false;
            }
            
            if (isValid) {
                alert('留言发送成功！我们会尽快回复您。');
                document.getElementById('contact-form').reset();
            }
        }
        
        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + '-error');
            field.classList.add('error');
            errorDiv.textContent = message;
        }
        
        function clearErrors() {
            const errorFields = document.querySelectorAll('.error');
            const errorMsgs = document.querySelectorAll('.error-msg');
            
            errorFields.forEach(field => field.classList.remove('error'));
            errorMsgs.forEach(msg => msg.textContent = '');
        }
        
        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const isOpen = answer.style.display === 'block';
            
            answer.style.display = isOpen ? 'none' : 'block';
            element.innerHTML = element.innerHTML.replace(isOpen ? '▲' : '▼', isOpen ? '▼' : '▲');
        }
        
        function toggleOption(checkbox) {
            updateSelectAll();
        }
        
        function selectAll(allCheckbox) {
            const checkboxes = document.querySelectorAll('#newsletter, #updates');
            checkboxes.forEach(cb => cb.checked = allCheckbox.checked);
        }
        
        function updateSelectAll() {
            const newsletter = document.getElementById('newsletter').checked;
            const updates = document.getElementById('updates').checked;
            const allOptions = document.getElementById('all-options');
            
            allOptions.checked = newsletter && updates;
        }
    </script>
</body>
</html>
