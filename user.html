<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - 购物商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>购物商城</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li class="dropdown">
                        <a href="products.html">商品分类</a>
                        <ul class="dropdown-menu">
                            <li><a href="products.html?category=electronics">电子产品</a></li>
                            <li><a href="products.html?category=clothing">服装</a></li>
                            <li><a href="products.html?category=books">图书</a></li>
                            <li><a href="products.html?category=home">家居</a></li>
                        </ul>
                    </li>
                    <li><a href="cart.html">购物车 <span id="cart-count">0</span></a></li>
                    <li><a href="user.html">用户中心</a></li>
                </ul>
            </nav>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索商品...">
                <div id="search-suggestions"></div>
                <button onclick="searchProducts()">搜索</button>
            </div>
        </div>
    </header>

    <main>
        <section class="user-section">
            <div class="container">
                <h1>用户中心</h1>
                
                <div class="user-tabs">
                    <div class="tabs">
                        <button class="tab active" onclick="switchTab('login')">登录</button>
                        <button class="tab" onclick="switchTab('register')">注册</button>
                        <button class="tab" onclick="switchTab('profile')">个人信息</button>
                        <button class="tab" onclick="switchTab('orders')">我的订单</button>
                    </div>
                    
                    <div class="tab-content active" id="login">
                        <div class="form-container">
                            <h2>用户登录</h2>
                            <form id="login-form" onsubmit="return handleLogin(event)">
                                <div class="form-group">
                                    <label for="login-email">邮箱:</label>
                                    <input type="email" id="login-email" name="email" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="login-password">密码:</label>
                                    <input type="password" id="login-password" name="password" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="remember-me">
                                    <label for="remember-me">记住我</label>
                                </div>
                                <button type="submit" class="btn-primary">登录</button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="register">
                        <div class="form-container">
                            <h2>用户注册</h2>
                            <form id="register-form" onsubmit="return handleRegister(event)">
                                <div class="form-group">
                                    <label for="register-username">用户名:</label>
                                    <input type="text" id="register-username" name="username" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="register-email">邮箱:</label>
                                    <input type="email" id="register-email" name="email" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="register-password">密码:</label>
                                    <input type="password" id="register-password" name="password" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="confirm-password">确认密码:</label>
                                    <input type="password" id="confirm-password" name="confirmPassword" required>
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="register-phone">手机号:</label>
                                    <input type="tel" id="register-phone" name="phone" required>
                                    <div class="error-message"></div>
                                </div>
                                <button type="submit" class="btn-primary">注册</button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="profile">
                        <div class="form-container">
                            <h2>个人信息</h2>
                            <form id="profile-form" onsubmit="return handleProfileUpdate(event)">
                                <div class="form-group">
                                    <label for="profile-username">用户名:</label>
                                    <input type="text" id="profile-username" name="username" value="示例用户">
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="profile-email">邮箱:</label>
                                    <input type="email" id="profile-email" name="email" value="<EMAIL>">
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="profile-phone">手机号:</label>
                                    <input type="tel" id="profile-phone" name="phone" value="13800138000">
                                    <div class="error-message"></div>
                                </div>
                                <div class="form-group">
                                    <label for="profile-address">地址:</label>
                                    <input type="text" id="profile-address" name="address" value="北京市朝阳区">
                                    <div class="error-message"></div>
                                </div>
                                <button type="submit" class="btn-primary">更新信息</button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="tab-content" id="orders">
                        <div class="orders-container">
                            <h2>我的订单</h2>
                            <div class="order-filters">
                                <button class="filter-btn active" onclick="filterOrders('all')">全部</button>
                                <button class="filter-btn" onclick="filterOrders('pending')">待付款</button>
                                <button class="filter-btn" onclick="filterOrders('shipped')">已发货</button>
                                <button class="filter-btn" onclick="filterOrders('completed')">已完成</button>
                            </div>
                            
                            <div class="orders-list" id="orders-list">
                                <div class="order-item" data-status="completed">
                                    <div class="order-header">
                                        <span class="order-number">订单号: 202401001</span>
                                        <span class="order-status completed">已完成</span>
                                    </div>
                                    <div class="order-products">
                                        <div class="order-product">
                                            <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; width: 60px; height: 60px;">智能手机</div>
                                            <div class="product-info">
                                                <h4>智能手机</h4>
                                                <p>数量: 1</p>
                                            </div>
                                            <div class="product-price">¥2999</div>
                                        </div>
                                    </div>
                                    <div class="order-total">总计: ¥2999</div>
                                </div>
                                
                                <div class="order-item" data-status="shipped">
                                    <div class="order-header">
                                        <span class="order-number">订单号: 202401002</span>
                                        <span class="order-status shipped">已发货</span>
                                    </div>
                                    <div class="order-products">
                                        <div class="order-product">
                                            <div class="product-img" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; display: flex; align-items: center; justify-content: center; width: 60px; height: 60px;">无线耳机</div>
                                            <div class="product-info">
                                                <h4>无线耳机</h4>
                                                <p>数量: 2</p>
                                            </div>
                                            <div class="product-price">¥598</div>
                                        </div>
                                    </div>
                                    <div class="order-total">总计: ¥598</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 购物商城. 保留所有权利.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/user.js"></script>
</body>
</html>
