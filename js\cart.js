document.addEventListener('DOMContentLoaded', function() {
    updateCartDisplay();
    updateCartSummary();
});

function updateCartDisplay() {
    const cartItems = document.getElementById('cart-items');
    const cartEmpty = document.getElementById('cart-empty');
    
    if (!cartItems) return;
    
    cartItems.innerHTML = '';
    
    if (cart.length === 0) {
        cartEmpty.style.display = 'block';
        cartItems.style.display = 'none';
        return;
    }
    
    cartEmpty.style.display = 'none';
    cartItems.style.display = 'block';
    
    cart.forEach((item, index) => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <div class="item-select">
                <input type="checkbox" id="item-${item.id}" class="item-checkbox" onchange="updateCartSummary()">
            </div>
            <div class="item-image">
                <div class="product-img" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; display: flex; align-items: center; justify-content: center; width: 80px; height: 80px;">${item.name}</div>
            </div>
            <div class="item-info">
                <h4>${item.name}</h4>
                <p class="item-price">¥${item.price}</p>
            </div>
            <div class="item-quantity">
                <div class="quantity-controls">
                    <button onclick="updateItemQuantity('${item.id}', -1)">-</button>
                    <input type="number" value="${item.quantity}" min="1" max="10" onchange="setItemQuantity('${item.id}', this.value)">
                    <button onclick="updateItemQuantity('${item.id}', 1)">+</button>
                </div>
            </div>
            <div class="item-total">
                <span>¥${(item.price * item.quantity).toFixed(2)}</span>
            </div>
            <div class="item-actions">
                <button onclick="removeCartItem('${item.id}')" class="remove-btn">删除</button>
            </div>
        `;
        cartItems.appendChild(cartItem);
    });
    
    updateCartCount();
}

function updateItemQuantity(id, change) {
    const item = cart.find(item => item.id === id);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeCartItem(id);
        } else if (item.quantity > 10) {
            item.quantity = 10;
        } else {
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartDisplay();
            updateCartSummary();
        }
    }
}

function setItemQuantity(id, quantity) {
    const item = cart.find(item => item.id === id);
    if (item) {
        const newQuantity = parseInt(quantity);
        if (newQuantity > 0 && newQuantity <= 10) {
            item.quantity = newQuantity;
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartDisplay();
            updateCartSummary();
        }
    }
}

function removeCartItem(id) {
    cart = cart.filter(item => item.id !== id);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartDisplay();
    updateCartSummary();
}

function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    itemCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateCartSummary();
}

function deleteSelected() {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    
    if (selectedItems.length === 0) {
        alert('请选择要删除的商品');
        return;
    }
    
    if (confirm('确定要删除选中的商品吗？')) {
        selectedItems.forEach(checkbox => {
            const itemId = checkbox.id.replace('item-', '');
            removeCartItem(itemId);
        });
    }
}

function clearCart() {
    if (cart.length === 0) {
        alert('购物车已经是空的');
        return;
    }
    
    if (confirm('确定要清空购物车吗？')) {
        cart = [];
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
        updateCartSummary();
    }
}

function updateCartSummary() {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    let totalItems = 0;
    let subtotal = 0;
    
    selectedItems.forEach(checkbox => {
        const itemId = checkbox.id.replace('item-', '');
        const item = cart.find(item => item.id === itemId);
        if (item) {
            totalItems += item.quantity;
            subtotal += item.price * item.quantity;
        }
    });
    
    const shipping = subtotal > 0 ? (subtotal >= 99 ? 0 : 10) : 0;
    const total = subtotal + shipping;
    
    document.getElementById('total-items').textContent = totalItems;
    document.getElementById('subtotal').textContent = `¥${subtotal.toFixed(2)}`;
    document.getElementById('shipping').textContent = `¥${shipping.toFixed(2)}`;
    document.getElementById('total-price').textContent = `¥${total.toFixed(2)}`;
    
    const selectAllCheckbox = document.getElementById('select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const checkedItems = document.querySelectorAll('.item-checkbox:checked');
    
    if (itemCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedItems.length === itemCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedItems.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

function checkout() {
    const selectedItems = document.querySelectorAll('.item-checkbox:checked');
    
    if (selectedItems.length === 0) {
        alert('请选择要结算的商品');
        return;
    }
    
    const totalPrice = document.getElementById('total-price').textContent;
    
    if (confirm(`确定要结算吗？总金额：${totalPrice}`)) {
        alert('订单提交成功！感谢您的购买！');
        
        selectedItems.forEach(checkbox => {
            const itemId = checkbox.id.replace('item-', '');
            removeCartItem(itemId);
        });
    }
}
