/* 现代化重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --text-color: #334155;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --border-radius: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* 现代化头部设计 */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

header .container {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 2rem;
    padding: 1rem 1.5rem;
}

.logo h1 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.cart-badge {
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    padding: 0.125rem 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.25rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    left: 0;
    background: white;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    border-bottom: 1px solid var(--border-color);
}

.dropdown-menu li:last-child {
    border-bottom: none;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.875rem;
}

.dropdown-menu a:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

/* 现代化搜索框 */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.search-container:focus-within {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.search-input {
    border: none;
    outline: none;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    background: transparent;
    flex: 1;
    min-width: 250px;
}

.search-input::placeholder {
    color: #94a3b8;
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.search-btn:hover {
    background: var(--secondary-color);
}

.search-suggestions {
    position: absolute;
    top: calc(100% + 0.25rem);
    left: 0;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    max-height: 200px;
    overflow-y: auto;
    display: none;
    z-index: 1001;
}

.search-container:focus-within .search-suggestions {
    display: block;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    font-size: 0.875rem;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

/* 主要内容区域 */
main {
    margin-top: 5rem;
}

/* 纯CSS轮播图 */
.hero-section {
    margin-bottom: 4rem;
}

.slider {
    position: relative;
    height: 500px;
    overflow: hidden;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    margin: 0 1.5rem;
}

.slider input[type="radio"] {
    display: none;
}

.slides {
    position: relative;
    width: 300%;
    height: 100%;
    display: flex;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide {
    width: 33.333%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.slide1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.slide2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.slide3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.slide-content {
    text-align: center;
    color: white;
    z-index: 2;
    max-width: 600px;
    padding: 2rem;
}

.slide-content h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.cta-btn {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.cta-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 轮播图导航 */
.navigation {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.nav-btn {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.8);
}

/* 轮播图切换效果 */
#slide1:checked ~ .slides {
    transform: translateX(0);
}

#slide2:checked ~ .slides {
    transform: translateX(-33.333%);
}

#slide3:checked ~ .slides {
    transform: translateX(-66.666%);
}

#slide1:checked ~ .navigation label:nth-child(1),
#slide2:checked ~ .navigation label:nth-child(2),
#slide3:checked ~ .navigation label:nth-child(3) {
    background: white;
    border-color: rgba(255, 255, 255, 0.5);
}

/* 产品展示区域 */
.featured-products {
    padding: 4rem 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.featured-products h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    position: relative;
}

.featured-products h2::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 0.25rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 0 1.5rem;
}

.product-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-0.5rem);
    box-shadow: var(--shadow-xl);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.new {
    background: var(--success-color);
}

.product-badge.discount {
    background: var(--warning-color);
}

.product-img {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.product-img.phone {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-img.laptop {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.product-img.earphone {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.product-img.watch {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.product-icon {
    font-size: 4rem;
    opacity: 0.9;
    transition: var(--transition);
}

.product-card:hover .product-icon {
    transform: scale(1.1);
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.product-desc {
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.price-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.current-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--danger-color);
}

.original-price {
    font-size: 1rem;
    color: #94a3b8;
    text-decoration: line-through;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    font-size: 0.875rem;
}

.rating-text {
    font-size: 0.75rem;
    color: #64748b;
}

.add-to-cart {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    text-align: center;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition);
    display: block;
}

.add-to-cart:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

/* 倒计时区域 */
.countdown-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%);
    color: white;
    text-align: center;
}

.countdown-section h2 {
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-weight: 700;
}

.countdown-desc {
    margin-bottom: 2rem;
    font-size: 1.125rem;
    opacity: 0.8;
}

.countdown-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.time-unit {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 100px;
}

.time-number {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.time-label {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.time-separator {
    font-size: 2rem;
    font-weight: 700;
    opacity: 0.6;
}

.flash-sale-products {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.flash-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    min-width: 150px;
}

.flash-item:hover {
    transform: translateY(-0.25rem);
    background: rgba(255, 255, 255, 0.15);
}

.flash-img {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.flash-info h4 {
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.flash-price {
    color: var(--warning-color);
    font-weight: 700;
    font-size: 1.125rem;
}

/* 现代化页脚 */
footer {
    background: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 700;
}

.footer-section h4 {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #e2e8f0;
}

.footer-section p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #cbd5e1;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.875rem;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links span {
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.social-links span:hover {
    transform: scale(1.2);
}

.footer-bottom {
    border-top: 1px solid #475569;
    padding-top: 1rem;
    text-align: center;
    font-size: 0.875rem;
    opacity: 0.8;
}

.form-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input.error {
    border-color: #ff6b6b;
}

.error-message {
    color: #ff6b6b;
    font-size: 0.9rem;
    margin-top: 0.25rem;
    display: none;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 2rem;
}

.tab {
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s;
}

.tab.active {
    background: #ff6b6b;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.cart-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 1rem;
}

.cart-item-info {
    flex: 1;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
}

.cart-total {
    text-align: right;
    padding: 2rem;
    font-size: 1.5rem;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .slider {
        height: 400px;
        margin: 0 1rem;
    }

    .slide-content h2 {
        font-size: 2.5rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    header .container {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .main-nav ul {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .search-input {
        min-width: 200px;
    }

    .slider {
        height: 350px;
        margin: 0 0.5rem;
    }

    .slide-content {
        padding: 1rem;
    }

    .slide-content h2 {
        font-size: 2rem;
    }

    .slide-content p {
        font-size: 1rem;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .countdown-display {
        gap: 0.5rem;
    }

    .time-unit {
        padding: 1rem;
        min-width: 80px;
    }

    .time-number {
        font-size: 2rem;
    }

    .flash-sale-products {
        gap: 1rem;
    }

    .flash-item {
        min-width: 120px;
        padding: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    header .container {
        padding: 0.75rem 1rem;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .main-nav ul {
        gap: 0.5rem;
    }

    .main-nav a {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .slider {
        height: 300px;
        margin: 0;
        border-radius: 0;
    }

    .slide-content h2 {
        font-size: 1.75rem;
    }

    .slide-content p {
        font-size: 0.875rem;
    }

    .cta-btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .featured-products {
        padding: 2rem 0;
    }

    .featured-products h2 {
        font-size: 2rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0;
    }

    .countdown-section {
        padding: 2rem 0;
    }

    .countdown-section h2 {
        font-size: 2rem;
    }

    .countdown-display {
        flex-direction: column;
        gap: 1rem;
    }

    .time-separator {
        display: none;
    }

    .flash-sale-products {
        flex-direction: column;
        align-items: center;
    }

    footer {
        padding: 2rem 0 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(2rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-card {
    animation: fadeInUp 0.6s ease-out;
}

.product-card:nth-child(1) { animation-delay: 0.1s; }
.product-card:nth-child(2) { animation-delay: 0.2s; }
.product-card:nth-child(3) { animation-delay: 0.3s; }
.product-card:nth-child(4) { animation-delay: 0.4s; }

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 商品列表页面样式 */
.products-section {
    padding: 2rem 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    min-height: 100vh;
}

.products-header {
    text-align: center;
    margin-bottom: 3rem;
}

.products-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 2rem;
    position: relative;
}

.products-header h1::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4rem;
    height: 0.25rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

/* 纯CSS筛选器 */
.filter-tabs input[type="radio"] {
    display: none;
}

.tab-labels {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.filter-tab {
    padding: 0.75rem 1.5rem;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    color: var(--text-color);
    box-shadow: var(--shadow-sm);
}

.filter-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 选中状态 */
#all:checked ~ .tab-labels label[for="all"],
#electronics:checked ~ .tab-labels label[for="electronics"],
#clothing:checked ~ .tab-labels label[for="clothing"],
#books:checked ~ .tab-labels label[for="books"],
#home:checked ~ .tab-labels label[for="home"] {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

/* 筛选显示逻辑 */
.product-card {
    display: block;
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease;
}

/* 默认显示全部 */
#all:checked ~ .tab-labels ~ .products-grid .product-card {
    display: block;
}

/* 电子产品筛选 */
#electronics:checked ~ .tab-labels ~ .products-grid .product-card {
    display: none;
}
#electronics:checked ~ .tab-labels ~ .products-grid .product-card.electronics {
    display: block;
}

/* 服装筛选 */
#clothing:checked ~ .tab-labels ~ .products-grid .product-card {
    display: none;
}
#clothing:checked ~ .tab-labels ~ .products-grid .product-card.clothing {
    display: block;
}

/* 图书筛选 */
#books:checked ~ .tab-labels ~ .products-grid .product-card {
    display: none;
}
#books:checked ~ .tab-labels ~ .products-grid .product-card.books {
    display: block;
}

/* 家居筛选 */
#home:checked ~ .tab-labels ~ .products-grid .product-card {
    display: none;
}
#home:checked ~ .tab-labels ~ .products-grid .product-card.home {
    display: block;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.view-details {
    display: block;
    text-align: center;
    padding: 0.5rem;
    background: #667eea;
    color: white;
    text-decoration: none;
    margin-top: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.view-details:hover {
    background: #5a67d8;
}

.product-detail-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-image-container {
    position: relative;
    display: flex;
    gap: 1rem;
}

.main-image {
    position: relative;
    flex: 1;
    cursor: crosshair;
}

.zoom-lens {
    position: absolute;
    border: 2px solid #ff6b6b;
    width: 100px;
    height: 100px;
    display: none;
    pointer-events: none;
    background: rgba(255, 107, 107, 0.1);
}

.zoom-result {
    width: 300px;
    height: 300px;
    border: 1px solid #ddd;
    display: none;
    position: relative;
    overflow: hidden;
}

.thumbnail-images {
    display: flex;
    gap: 0.5rem;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: border-color 0.3s;
}

.thumbnail.active {
    border-color: #ff6b6b;
}

.product-info h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 2rem;
    color: #ff6b6b;
    font-weight: bold;
    margin-bottom: 1rem;
}

.product-description {
    margin-bottom: 2rem;
}

.product-options {
    margin-bottom: 2rem;
}

.option-group {
    margin-bottom: 1rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.color-options, .storage-options {
    display: flex;
    gap: 0.5rem;
}

.color-option, .storage-option {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s;
}

.color-option.active, .storage-option.active {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-selector button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
}

.quantity-selector input {
    width: 60px;
    text-align: center;
    border: 1px solid #ddd;
    padding: 0.25rem;
}

.product-actions {
    display: flex;
    gap: 1rem;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-secondary:hover {
    background: #5a6268;
}

.product-tabs {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
}

.specs-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.specs-table td:first-child {
    font-weight: bold;
    width: 30%;
}

.review-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.reviewer {
    font-weight: bold;
}

.rating {
    color: #ffc107;
}

.cart-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.cart-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
    margin-bottom: 1rem;
}

.select-all {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-actions {
    display: flex;
    gap: 1rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-danger:hover {
    background: #c82333;
}

.cart-item {
    display: grid;
    grid-template-columns: auto 80px 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.item-select, .item-image, .item-info, .item-quantity, .item-total, .item-actions {
    display: flex;
    align-items: center;
}

.item-info h4 {
    margin-bottom: 0.5rem;
}

.item-price {
    color: #666;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 3px;
    cursor: pointer;
}

.cart-summary {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    font-size: 1.2rem;
    font-weight: bold;
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
    margin-top: 1rem;
}

.checkout-btn {
    width: 100%;
    background: #28a745;
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: background 0.3s;
}

.checkout-btn:hover {
    background: #218838;
}

.empty-message {
    text-align: center;
    padding: 3rem;
}

.empty-message h3 {
    margin-bottom: 1rem;
    color: #666;
}

.empty-message p {
    margin-bottom: 2rem;
    color: #999;
}

.user-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.user-tabs {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.orders-container h2 {
    margin-bottom: 1rem;
}

.order-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s;
}

.filter-btn.active {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.order-item {
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 1rem;
    padding: 1rem;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.order-status {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.order-status.completed {
    background: #d4edda;
    color: #155724;
}

.order-status.shipped {
    background: #cce7ff;
    color: #004085;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-product {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.order-product .product-info h4 {
    margin-bottom: 0.25rem;
}

.order-total {
    text-align: right;
    font-weight: bold;
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #eee;
}