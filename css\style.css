* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    color: #ff6b6b;
    font-size: 1.8rem;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.main-nav a:hover {
    color: #ff6b6b;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 5px;
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s;
    flex-direction: column;
    gap: 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    padding: 0.5rem 1rem;
}

.dropdown-menu a {
    display: block;
    padding: 0.5rem 0;
}

.search-box {
    position: relative;
    display: flex;
    gap: 10px;
}

.search-box input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 200px;
}

.search-box button {
    padding: 0.5rem 1rem;
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

#search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 70px;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    z-index: 1001;
}

.suggestion-item {
    padding: 0.5rem;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background: #f5f5f5;
}

main {
    margin-top: 80px;
}

.hero-slider {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.slider-container {
    position: relative;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
}

.slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.slide-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.slide-content h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.slide-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.btn-primary {
    background: #ff6b6b;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-primary:hover {
    background: #ff5252;
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 3;
}

.slider-nav button {
    background: rgba(255,255,255,0.7);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: background 0.3s;
}

.slider-nav button:hover {
    background: rgba(255,255,255,0.9);
}

.slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: background 0.3s;
}

.dot.active {
    background: white;
}

.featured-products {
    padding: 4rem 0;
    background: #f8f9fa;
}

.featured-products h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    color: #333;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-card h3 {
    padding: 1rem;
    font-size: 1.2rem;
}

.price {
    padding: 0 1rem;
    font-size: 1.5rem;
    color: #ff6b6b;
    font-weight: bold;
}

.add-to-cart {
    width: 100%;
    padding: 1rem;
    background: #4ecdc4;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

.add-to-cart:hover {
    background: #45b7aa;
}

.countdown-section {
    padding: 4rem 0;
    background: #333;
    color: white;
    text-align: center;
}

.countdown-section h2 {
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.time-unit {
    text-align: center;
}

.time-unit span {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    background: #ff6b6b;
    padding: 1rem;
    border-radius: 10px;
    min-width: 80px;
}

.time-unit label {
    display: block;
    margin-top: 0.5rem;
    font-size: 1.2rem;
}

footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

#cart-count {
    background: #ff6b6b;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8rem;
    margin-left: 5px;
}

.form-container {
    max-width: 500px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input.error {
    border-color: #ff6b6b;
}

.error-message {
    color: #ff6b6b;
    font-size: 0.9rem;
    margin-top: 0.25rem;
    display: none;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 2rem;
}

.tab {
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s;
}

.tab.active {
    background: #ff6b6b;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.cart-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 1rem;
}

.cart-item-info {
    flex: 1;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
}

.cart-total {
    text-align: right;
    padding: 2rem;
    font-size: 1.5rem;
    font-weight: bold;
}

@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav ul {
        gap: 1rem;
    }

    .slide-content h2 {
        font-size: 2rem;
    }

    .countdown-timer {
        flex-direction: column;
        align-items: center;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
    }
}

.products-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.view-details {
    display: block;
    text-align: center;
    padding: 0.5rem;
    background: #667eea;
    color: white;
    text-decoration: none;
    margin-top: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.view-details:hover {
    background: #5a67d8;
}

.product-detail-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-image-container {
    position: relative;
    display: flex;
    gap: 1rem;
}

.main-image {
    position: relative;
    flex: 1;
    cursor: crosshair;
}

.zoom-lens {
    position: absolute;
    border: 2px solid #ff6b6b;
    width: 100px;
    height: 100px;
    display: none;
    pointer-events: none;
    background: rgba(255, 107, 107, 0.1);
}

.zoom-result {
    width: 300px;
    height: 300px;
    border: 1px solid #ddd;
    display: none;
    position: relative;
    overflow: hidden;
}

.thumbnail-images {
    display: flex;
    gap: 0.5rem;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: border-color 0.3s;
}

.thumbnail.active {
    border-color: #ff6b6b;
}

.product-info h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.product-price {
    font-size: 2rem;
    color: #ff6b6b;
    font-weight: bold;
    margin-bottom: 1rem;
}

.product-description {
    margin-bottom: 2rem;
}

.product-options {
    margin-bottom: 2rem;
}

.option-group {
    margin-bottom: 1rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.color-options, .storage-options {
    display: flex;
    gap: 0.5rem;
}

.color-option, .storage-option {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s;
}

.color-option.active, .storage-option.active {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-selector button {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
}

.quantity-selector input {
    width: 60px;
    text-align: center;
    border: 1px solid #ddd;
    padding: 0.25rem;
}

.product-actions {
    display: flex;
    gap: 1rem;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-secondary:hover {
    background: #5a6268;
}

.product-tabs {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
}

.specs-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.specs-table td:first-child {
    font-weight: bold;
    width: 30%;
}

.review-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.reviewer {
    font-weight: bold;
}

.rating {
    color: #ffc107;
}

.cart-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.cart-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
    margin-bottom: 1rem;
}

.select-all {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-actions {
    display: flex;
    gap: 1rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-danger:hover {
    background: #c82333;
}

.cart-item {
    display: grid;
    grid-template-columns: auto 80px 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.item-select, .item-image, .item-info, .item-quantity, .item-total, .item-actions {
    display: flex;
    align-items: center;
}

.item-info h4 {
    margin-bottom: 0.5rem;
}

.item-price {
    color: #666;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 3px;
    cursor: pointer;
}

.cart-summary {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    font-size: 1.2rem;
    font-weight: bold;
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
    margin-top: 1rem;
}

.checkout-btn {
    width: 100%;
    background: #28a745;
    color: white;
    padding: 1rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: background 0.3s;
}

.checkout-btn:hover {
    background: #218838;
}

.empty-message {
    text-align: center;
    padding: 3rem;
}

.empty-message h3 {
    margin-bottom: 1rem;
    color: #666;
}

.empty-message p {
    margin-bottom: 2rem;
    color: #999;
}

.user-section {
    padding: 2rem 0;
    background: #f8f9fa;
}

.user-tabs {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.orders-container h2 {
    margin-bottom: 1rem;
}

.order-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.3s;
}

.filter-btn.active {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.order-item {
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 1rem;
    padding: 1rem;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.order-status {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.9rem;
}

.order-status.completed {
    background: #d4edda;
    color: #155724;
}

.order-status.shipped {
    background: #cce7ff;
    color: #004085;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-product {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.order-product .product-info h4 {
    margin-bottom: 0.25rem;
}

.order-total {
    text-align: right;
    font-weight: bold;
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #eee;
}