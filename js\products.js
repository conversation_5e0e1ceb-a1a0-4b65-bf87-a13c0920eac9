document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const category = urlParams.get('category');
    const searchQuery = urlParams.get('search');
    
    if (category) {
        document.getElementById('category-filter').value = category;
        filterProducts();
    }
    
    if (searchQuery) {
        document.getElementById('search-input').value = searchQuery;
        searchProducts();
    }
});

function filterProducts() {
    const categoryFilter = document.getElementById('category-filter').value;
    const priceFilter = document.getElementById('price-filter').value;
    const productCards = document.querySelectorAll('.product-card');
    
    let visibleProducts = Array.from(productCards);
    
    if (categoryFilter) {
        visibleProducts = visibleProducts.filter(card => 
            card.getAttribute('data-category') === categoryFilter
        );
    }
    
    productCards.forEach(card => {
        if (visibleProducts.includes(card)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
    
    if (priceFilter) {
        sortProducts(visibleProducts, priceFilter);
    }
}

function sortProducts(products, sortType) {
    const container = document.getElementById('products-grid');
    
    products.sort((a, b) => {
        const priceA = parseFloat(a.getAttribute('data-price'));
        const priceB = parseFloat(b.getAttribute('data-price'));
        
        if (sortType === 'low-high') {
            return priceA - priceB;
        } else if (sortType === 'high-low') {
            return priceB - priceA;
        }
        return 0;
    });
    
    products.forEach(product => {
        container.appendChild(product);
    });
}

function searchProducts() {
    const searchInput = document.getElementById('search-input');
    const query = searchInput.value.toLowerCase().trim();
    const productCards = document.querySelectorAll('.product-card');
    
    if (!query) {
        productCards.forEach(card => {
            card.style.display = 'block';
        });
        return;
    }
    
    productCards.forEach(card => {
        const title = card.querySelector('h3').textContent.toLowerCase();
        const description = card.querySelector('.description').textContent.toLowerCase();
        
        if (title.includes(query) || description.includes(query)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}
